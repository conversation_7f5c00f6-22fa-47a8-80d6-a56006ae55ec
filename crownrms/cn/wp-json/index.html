{"name": "Crown Information Management China", "description": "", "url": "https://www.crownrms.com/cn", "home": "https://www.crownrms.com/cn", "gmt_offset": "0", "timezone_string": "", "page_for_posts": 0, "page_on_front": 1507, "show_on_front": "page", "namespaces": ["oembed/1.0", "wpe/cache-plugin/v1", "wpe_sign_on_plugin/v1", "v1", "moove-update-checker/v1", "moove-ajax-debug/v1", "redirection/v1", "wpml/v1", "wordfence/v1", "yoast/v1", "wpml/st/v1", "wpml/tm/v1", "wpml/ate/v1", "gravityforms/v2", "wp-rocket/v1", "mic/v1", "otgs/installer/v1", "facetwp/v1", "wp/v2", "wp-site-health/v1", "wp-block-editor/v1"], "authentication": [], "routes": {"/": {"namespace": "", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/"}]}}, "/batch/v1": {"namespace": "", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"validation": {"type": "string", "enum": ["require-all-validate", "normal"], "default": "normal", "required": false}, "requests": {"type": "array", "maxItems": 25, "items": {"type": "object", "properties": {"method": {"type": "string", "enum": ["POST", "PUT", "PATCH", "DELETE"], "default": "POST"}, "path": {"type": "string", "required": true}, "body": {"type": "object", "properties": [], "additionalProperties": true}, "headers": {"type": "object", "properties": [], "additionalProperties": {"type": ["string", "array"], "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/batch/v1"}]}}, "/oembed/1.0": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "oembed/1.0", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/oembed/1.0"}]}}, "/oembed/1.0/embed": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "需要获取 oEmbed 数据的链接。", "type": "string", "format": "uri", "required": true}, "format": {"default": "json", "required": false}, "maxwidth": {"default": 600, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/oembed/1.0/embed"}]}}, "/oembed/1.0/proxy": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "需要获取 oEmbed 数据的链接。", "type": "string", "format": "uri", "required": true}, "format": {"description": "使用的 oEmbed 格式。", "type": "string", "default": "json", "enum": ["json", "xml"], "required": false}, "maxwidth": {"description": "嵌入的元素的最大宽度（像素）。", "type": "integer", "default": 600, "required": false}, "maxheight": {"description": "嵌入的元素的最大高度（像素）。", "type": "integer", "required": false}, "discover": {"description": "对未经批准的提供者是否进行 oEmbed 发现请求。", "type": "boolean", "default": true, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/oembed/1.0/proxy"}]}}, "/wpe/cache-plugin/v1": {"namespace": "wpe/cache-plugin/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wpe/cache-plugin/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe/cache-plugin/v1"}]}}, "/wpe/cache-plugin/v1/clear_all_caches": {"namespace": "wpe/cache-plugin/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe/cache-plugin/v1/clear_all_caches"}]}}, "/wpe/cache-plugin/v1/rate_limit_status": {"namespace": "wpe/cache-plugin/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe/cache-plugin/v1/rate_limit_status"}]}}, "/wpe_sign_on_plugin/v1": {"namespace": "wpe_sign_on_plugin/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wpe_sign_on_plugin/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe_sign_on_plugin/v1"}]}}, "/wpe_sign_on_plugin/v1/login": {"namespace": "wpe_sign_on_plugin/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe_sign_on_plugin/v1/login"}]}}, "/wpe_sign_on_plugin/v1/is_user_logged_in": {"namespace": "wpe_sign_on_plugin/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe_sign_on_plugin/v1/is_user_logged_in"}]}}, "/wpe_sign_on_plugin/v1/has_logged": {"namespace": "wpe_sign_on_plugin/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpe_sign_on_plugin/v1/has_logged"}]}}, "/v1": {"namespace": "v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/v1"}]}}, "/v1/gtm-report": {"namespace": "v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/v1/gtm-report"}]}}, "/moove-update-checker/v1": {"namespace": "moove-update-checker/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "moove-update-checker/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/moove-update-checker/v1"}]}}, "/moove-update-checker/v1/core": {"namespace": "moove-update-checker/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/moove-update-checker/v1/core"}]}}, "/moove-ajax-debug/v1": {"namespace": "moove-ajax-debug/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "moove-ajax-debug/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/moove-ajax-debug/v1"}]}}, "/moove-ajax-debug/v1/core": {"namespace": "moove-ajax-debug/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/moove-ajax-debug/v1/core"}]}}, "/redirection/v1": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "redirection/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1"}]}}, "/redirection/v1/redirect": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/redirect"}]}}, "/redirection/v1/redirect/(?P<id>[\\d]+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/redirect/post": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"text": {"description": "Text to match", "type": "string", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/redirect/post"}]}}, "/redirection/v1/bulk/redirect/(?P<bulk>delete|enable|disable|reset)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["source", "last_count", "last_access", "position", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "global": {"description": "Apply bulk action globally, as per filters", "type": "boolean", "required": false}, "items": {"description": "Array of IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/group": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "moduleId": {"description": "Module ID", "type": "integer", "minimum": 0, "maximum": 3, "required": true}, "name": {"description": "Group name", "type": "string", "required": true}, "status": {"description": "Status of the group", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/group"}]}}, "/redirection/v1/group/(?P<id>[\\d]+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"moduleId": {"description": "Module ID", "type": "integer", "minimum": 0, "maximum": 3, "required": true}, "name": {"description": "Group name", "type": "string", "required": true}, "status": {"description": "Status of the group", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/bulk/group/(?P<bulk>delete|enable|disable)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["name", "id", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/log": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/log"}]}}, "/redirection/v1/bulk/log/(?P<bulk>delete)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/404": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/404"}]}}, "/redirection/v1/bulk/404/(?P<bulk>delete)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"filterBy": {"description": "Field to filter by", "required": false}, "orderby": {"description": "Field to order results by", "type": "string", "enum": ["url", "ip", "total", "count", ""], "required": false}, "direction": {"description": "Direction of ordered results", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "per_page": {"description": "Number of results per page", "type": "integer", "default": 25, "minimum": 5, "maximum": 200, "required": false}, "page": {"description": "Page offset", "type": "integer", "minimum": 0, "default": 0, "required": false}, "items": {"description": "Comma separated list of item IDs to perform action on", "type": "array", "items": {"description": "Item ID", "type": ["string", "number"]}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/setting": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/setting"}]}}, "/redirection/v1/plugin": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"name": {"description": "Name", "type": "string", "required": false}, "value": {"description": "Value", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/plugin"}]}}, "/redirection/v1/plugin/delete": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/plugin/delete"}]}}, "/redirection/v1/plugin/test": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/plugin/test"}]}}, "/redirection/v1/plugin/data": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"upgrade": {"description": "Upgrade parameter", "type": "string", "enum": ["stop", "skip", "retry"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/plugin/data"}]}}, "/redirection/v1/import/file/(?P<group_id>\\d+)": {"namespace": "redirection/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/redirection/v1/import/plugin": {"namespace": "redirection/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/redirection/v1/import/plugin"}]}}, "/redirection/v1/export/(?P<module>1|2|3|all)/(?P<format>csv|apache|nginx|json)": {"namespace": "redirection/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wpml/v1": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wpml/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1"}]}}, "/wpml/v1/custom-xml-config": {"namespace": "wpml/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/custom-xml-config"}]}}, "/wpml/v1/custom-xml-config/validate": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/custom-xml-config/validate"}]}}, "/wordfence/v1": {"namespace": "wordfence/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wordfence/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1"}]}}, "/wordfence/v1/authenticate": {"namespace": "wordfence/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/authenticate"}]}}, "/wordfence/v1/authenticate-premium": {"namespace": "wordfence/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/authenticate-premium"}]}}, "/wordfence/v1/config": {"namespace": "wordfence/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/config"}]}}, "/wordfence/v1/disconnect": {"namespace": "wordfence/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/disconnect"}]}}, "/wordfence/v1/premium-connect": {"namespace": "wordfence/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/premium-connect"}]}}, "/wordfence/v1/scan/issues": {"namespace": "wordfence/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/scan/issues"}]}}, "/wordfence/v1/scan": {"namespace": "wordfence/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/scan"}]}}, "/wordfence/v1/scan/issue": {"namespace": "wordfence/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wordfence/v1/scan/issue"}]}}, "/yoast/v1": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "yoast/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1"}]}}, "/yoast/v1/file_size": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"type": "string", "description": "The url to retrieve", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/file_size"}]}}, "/yoast/v1/statistics": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/statistics"}]}}, "/yoast/v1/new-content-type-visibility/dismiss-post-type": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"postTypeName": {"required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/new-content-type-visibility/dismiss-post-type"}]}}, "/yoast/v1/new-content-type-visibility/dismiss-taxonomy": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"taxonomyName": {"required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/new-content-type-visibility/dismiss-taxonomy"}]}}, "/yoast/v1/site_kit_configuration_permanent_dismissal": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"is_dismissed": {"type": "bool", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/site_kit_configuration_permanent_dismissal"}]}}, "/yoast/v1/site_kit_manage_consent": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"consent": {"type": "bool", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/site_kit_manage_consent"}]}}, "/yoast/v1/readability_scores": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"contentType": {"type": "string", "required": true}, "taxonomy": {"type": "string", "default": "", "required": false}, "term": {"type": "integer", "default": null, "required": false}, "troubleshooting": {"type": "bool", "default": null, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/readability_scores"}]}}, "/yoast/v1/seo_scores": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"contentType": {"type": "string", "required": true}, "taxonomy": {"type": "string", "default": "", "required": false}, "term": {"type": "integer", "default": null, "required": false}, "troubleshooting": {"type": "bool", "default": null, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/seo_scores"}]}}, "/yoast/v1/introductions/(?P<introduction_id>[\\w-]+)/seen": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"introduction_id": {"type": "string", "required": true}, "is_seen": {"type": "bool", "default": true, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/yoast/v1/wistia_embed_permission": {"namespace": "yoast/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"value": {"type": "bool", "default": true, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/wistia_embed_permission"}]}}, "/yoast/v1/alerts/dismiss": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"key": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/alerts/dismiss"}]}}, "/yoast/v1/configuration/site_representation": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"company_or_person": {"type": "string", "enum": ["company", "person"], "required": true}, "company_name": {"type": "string", "required": false}, "company_logo": {"type": "string", "required": false}, "company_logo_id": {"type": "integer", "required": false}, "person_logo": {"type": "string", "required": false}, "person_logo_id": {"type": "integer", "required": false}, "company_or_person_user_id": {"type": "integer", "required": false}, "description": {"type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/configuration/site_representation"}]}}, "/yoast/v1/configuration/social_profiles": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"facebook_site": {"type": "string", "required": false}, "twitter_site": {"type": "string", "required": false}, "other_social_urls": {"type": "array", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/configuration/social_profiles"}]}}, "/yoast/v1/configuration/check_capability": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"user_id": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/configuration/check_capability"}]}}, "/yoast/v1/configuration/enable_tracking": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"tracking": {"type": "boolean", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/configuration/enable_tracking"}]}}, "/yoast/v1/configuration/save_configuration_state": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"finishedSteps": {"type": "array", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/configuration/save_configuration_state"}]}}, "/yoast/v1/configuration/get_configuration_state": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/configuration/get_configuration_state"}]}}, "/yoast/v1/import/(?P<plugin>[\\w-]+)/(?P<type>[\\w-]+)": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/yoast/v1/get_head": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/get_head"}]}}, "/yoast/v1/indexing/posts": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/posts"}]}}, "/yoast/v1/indexing/terms": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/terms"}]}}, "/yoast/v1/indexing/post-type-archives": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/post-type-archives"}]}}, "/yoast/v1/indexing/general": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/general"}]}}, "/yoast/v1/indexing/prepare": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/prepare"}]}}, "/yoast/v1/indexing/indexables-complete": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/indexables-complete"}]}}, "/yoast/v1/indexing/complete": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/indexing/complete"}]}}, "/yoast/v1/link-indexing/posts": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/link-indexing/posts"}]}}, "/yoast/v1/link-indexing/terms": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/link-indexing/terms"}]}}, "/yoast/v1/integrations/set_active": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"active": {"type": "boolean", "required": true}, "integration": {"type": "string", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/integrations/set_active"}]}}, "/yoast/v1/meta/search": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/meta/search"}]}}, "/yoast/v1/semrush/authenticate": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"code": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/semrush/authenticate"}]}}, "/yoast/v1/semrush/country_code": {"namespace": "yoast/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"country_code": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/semrush/country_code"}]}}, "/yoast/v1/semrush/related_keyphrases": {"namespace": "yoast/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"keyphrase": {"required": true}, "country_code": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/semrush/related_keyphrases"}]}}, "/yoast/v1/workouts": {"namespace": "yoast/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/yoast/v1/workouts"}]}}, "/wpml/st/v1": {"namespace": "wpml/st/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wpml/st/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1"}]}}, "/wpml/st/v1/strings/settings": {"namespace": "wpml/st/v1", "methods": ["POST", "GET"], "endpoints": [{"methods": ["POST"], "args": {"autoregisterType": {"type": "integer", "default": 0, "required": false}, "shouldRegisterBackendStrings": {"type": "integer", "default": 0, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/strings/settings"}]}}, "/wpml/st/v1/strings/itemscount": {"namespace": "wpml/st/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"kind": {"type": "integer", "default": null, "required": false}, "type": {"type": "string", "required": false}, "source": {"type": "integer", "default": null, "required": false}, "domain": {"type": "string", "default": null, "required": false}, "translationPriority": {"type": "string", "default": null, "required": false}, "title": {"type": "string", "required": false}, "sourceLanguageCode": {"type": "string", "required": false}, "targetLanguageCode": {"type": "string", "required": false}, "translationStatuses": {"default": [], "required": false}, "limit": {"type": "integer", "default": 10, "required": false}, "offset": {"type": "integer", "default": 0, "required": false}, "sorting": {"default": null, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/strings/itemscount"}]}}, "/wpml/st/v1/strings": {"namespace": "wpml/st/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"kind": {"type": "integer", "default": null, "required": false}, "type": {"type": "string", "required": false}, "source": {"type": "integer", "default": null, "required": false}, "domain": {"type": "string", "default": null, "required": false}, "translationPriority": {"type": "string", "default": null, "required": false}, "title": {"type": "string", "required": false}, "sourceLanguageCode": {"type": "string", "required": false}, "targetLanguageCode": {"type": "string", "required": false}, "translationStatuses": {"default": [], "required": false}, "limit": {"type": "integer", "default": 10, "required": false}, "offset": {"type": "integer", "default": 0, "required": false}, "sorting": {"default": null, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/strings"}]}}, "/wpml/st/v1/strings/filters": {"namespace": "wpml/st/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/strings/filters"}]}}, "/wpml/st/v1/string-packages": {"namespace": "wpml/st/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"type": "string", "required": false}, "title": {"type": "string", "required": false}, "sourceLanguageCode": {"type": "string", "required": false}, "targetLanguageCode": {"type": "string", "required": false}, "translationStatuses": {"default": [], "required": false}, "limit": {"type": "integer", "default": 10, "required": false}, "offset": {"type": "integer", "default": 0, "required": false}, "sorting": {"default": null, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/string-packages"}]}}, "/wpml/st/v1/strings/processstringsqueue": {"namespace": "wpml/st/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/strings/processstringsqueue"}]}}, "/wpml/tm/v1": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wpml/tm/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1"}]}}, "/wpml/tm/v1/ate/jobs/store": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_job_id": {"type": "string", "required": true}, "ate_job_data": {"type": "array", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ate/jobs/store"}]}}, "/wpml/tm/v1/xliff/fetch/(?P<jobId>\\d+)": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wpml/tm/v1/ams/register_manager": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ams/register_manager"}]}}, "/wpml/tm/v1/ams/synchronize/translators": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ams/synchronize/translators"}]}}, "/wpml/tm/v1/ams/synchronize/managers": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ams/synchronize/managers"}]}}, "/wpml/tm/v1/ams/status": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ams/status"}]}}, "/wpml/tm/v1/ams/console": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ams/console"}]}}, "/wpml/tm/v1/ams/engines": {"namespace": "wpml/tm/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ams/engines"}]}}, "/wpml/tm/v1/ate/jobs": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ate/jobs"}]}}, "/wpml/tm/v1/ate/jobs/(?P<ateJobId>\\d+)": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wpml/tm/v1/jobs": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"local_job_ids": {"type": "string", "required": false}, "scope": {"type": "string", "required": false}, "id": {"type": "integer", "required": false}, "title": {"type": "string", "required": false}, "source_language": {"type": "string", "required": false}, "target_language": {"type": "string", "required": false}, "status": {"type": "string", "required": false}, "needs_update": {"type": "string", "required": false}, "limit": {"type": "integer", "required": false}, "offset": {"type": "integer", "required": false}, "sorting": {"required": false}, "translated_by": {"type": "string", "required": false}, "sent_from": {"type": "string", "required": false}, "sent_to": {"type": "string", "required": false}, "deadline_from": {"type": "string", "required": false}, "deadline_to": {"type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/jobs"}]}}, "/wpml/tm/v1/jobs/assign": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"jobId": {"required": true}, "type": {"required": false}, "translatorId": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/jobs/assign"}]}}, "/wpml/tm/v1/jobs/cancel": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/jobs/cancel"}]}}, "/wpml/tm/v1/tp/xliff/download/(?P<job_id>\\d+)/(?P<job_type>\\w+)": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"job_id": {"required": true}, "job_type": {"required": true}, "json": {"type": "boolean", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wpml/tm/v1/tp/apply-translations": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/tp/apply-translations"}]}}, "/wpml/tm/v1/tp/batches/sync": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"batchId": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/tp/batches/sync"}]}}, "/wpml/tm/v1/tp/batches/status": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/tp/batches/status"}]}}, "/wpml/tm/v1/ate/jobs/sync": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"lockKey": {"type": "string", "required": false}, "ateToken": {"type": "string", "required": false}, "page": {"type": "int", "required": false}, "numberOfPages": {"type": "int", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ate/jobs/sync"}]}}, "/wpml/tm/v1/ate/jobs/download": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ate/jobs/download"}]}}, "/wpml/tm/v1/ate/jobs/retry": {"namespace": "wpml/tm/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/tm/v1/ate/jobs/retry"}]}}, "/wpml/tm/v1/ate/jobs/receive/(?P<wpmlJobId>\\d+)": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpmlJobId": {"type": "int", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wpml/tm/v1/ate/jobs/(?P<ateJobId>\\d+)/fix": {"namespace": "wpml/tm/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wpml/ate/v1": {"namespace": "wpml/ate/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wpml/ate/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/ate/v1"}]}}, "/wpml/ate/v1/ate/proxy": {"namespace": "wpml/ate/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/ate/v1/ate/proxy"}]}}, "/wpml/st/v1/import_mo_strings": {"namespace": "wpml/st/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"plugins": {"type": "array", "required": false}, "themes": {"type": "array", "required": false}, "other": {"type": "array", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/import_mo_strings"}]}}, "/wpml/st/v1/pre_generate_mo_files": {"namespace": "wpml/st/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/pre_generate_mo_files"}]}}, "/wpml/st/v1/settings": {"namespace": "wpml/st/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"group": {"required": true}, "key": {"required": true}, "data": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/st/v1/settings"}]}}, "/gravityforms/v2": {"namespace": "gravityforms/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "gravityforms/v2", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/gravityforms/v2"}]}}, "/gravityforms/v2/tests/mock-data": {"namespace": "gravityforms/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/gravityforms/v2/tests/mock-data"}]}}, "/wpml/v1/item-sections/populated": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/item-sections/populated"}]}}, "/wpml/v1/posts": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/posts"}]}}, "/wpml/v1/posts/count": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/posts/count"}]}}, "/wpml/v1/send-to-translation/validate-translation-options": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/send-to-translation/validate-translation-options"}]}}, "/wpml/v1/send-to-translation/default-batch-name": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/send-to-translation/default-batch-name"}]}}, "/wpml/v1/send-to-translation/validate-batch-name": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/send-to-translation/validate-batch-name"}]}}, "/wpml/v1/set-review-translation-option": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/set-review-translation-option"}]}}, "/wpml/v1/posts/hierarchical": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/posts/hierarchical"}]}}, "/wpml/v1/posts/taxonomies": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/posts/taxonomies"}]}}, "/wpml/v1/posts/terms": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/posts/terms"}]}}, "/wpml/v1/translate-everything/enable": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/translate-everything/enable"}]}}, "/wpml/v1/translate-everything/disable": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/translate-everything/disable"}]}}, "/wpml/v1/getuntranslatedtypescount": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/getuntranslatedtypescount"}]}}, "/wpml/v1/save-translator-note": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/save-translator-note"}]}}, "/wpml/v1/credits": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/credits"}]}}, "/wpml/v1/translation-proxy/commit-batch": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/translation-proxy/commit-batch"}]}}, "/wpml/v1/tranlsation-proxy/getlastpickedup": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/tranlsation-proxy/getlastpickedup"}]}}, "/wpml/v1/translation-proxy/getRemoteJobsCount": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/translation-proxy/getRemoteJobsCount"}]}}, "/wpml/v1/gettranslationstatus": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/gettranslationstatus"}]}}, "/wpml/v1/getlocaltranslatorbyid": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/getlocaltranslatorbyid"}]}}, "/wpml/v1/reloadremotetranslationservice": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/reloadremotetranslationservice"}]}}, "/wpml/v1/gettranslationeditortype": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/gettranslationeditortype"}]}}, "/wpml/v1/translate-existing-content": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/translate-existing-content"}]}}, "/wpml/v1/get-needs-update-count-created-in-cte": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/get-needs-update-count-created-in-cte"}]}}, "/wpml/v1/get-engines": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/get-engines"}]}}, "/wpml/v1/save-automatic-translation-settings": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/save-automatic-translation-settings"}]}}, "/wpml/v1/getlocaltranslators": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/getlocaltranslators"}]}}, "/wpml/v1/account-balances": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/account-balances"}]}}, "/wpml/v1/glossary-count": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/glossary-count"}]}}, "/wpml/v1/enable-ate": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/enable-ate"}]}}, "/wpml/v1/get-dismissed-notices": {"namespace": "wpml/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/get-dismissed-notices"}]}}, "/wpml/v1/dismiss-notice": {"namespace": "wpml/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wpml/v1/dismiss-notice"}]}}, "/wp-rocket/v1": {"namespace": "wp-rocket/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-rocket/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-rocket/v1"}]}}, "/wp-rocket/v1/rocketcdn/enable": {"namespace": "wp-rocket/v1", "methods": ["PUT"], "endpoints": [{"methods": ["PUT"], "args": {"email": {"required": true}, "key": {"required": true}, "url": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-rocket/v1/rocketcdn/enable"}]}}, "/wp-rocket/v1/rocketcdn/disable": {"namespace": "wp-rocket/v1", "methods": ["PUT"], "endpoints": [{"methods": ["PUT"], "args": {"email": {"required": true}, "key": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-rocket/v1/rocketcdn/disable"}]}}, "/wp-rocket/v1/cpcss/post/(?P<id>[\\d]+)": {"namespace": "wp-rocket/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp-rocket/v1/support": {"namespace": "wp-rocket/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"email": {"required": true}, "key": {"required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-rocket/v1/support"}]}}, "/wp-rocket/v1/dynamic_lists/update": {"namespace": "wp-rocket/v1", "methods": ["PUT"], "endpoints": [{"methods": ["PUT"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-rocket/v1/dynamic_lists/update"}]}}, "/wp-rocket/v1/options/export": {"namespace": "wp-rocket/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-rocket/v1/options/export"}]}}, "/mic/v1": {"namespace": "mic/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "mic/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/mic/v1"}]}}, "/mic/v1/cpid": {"namespace": "mic/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/mic/v1/cpid"}]}}, "/otgs/installer/v1": {"namespace": "otgs/installer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "otgs/installer/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/otgs/installer/v1"}]}}, "/otgs/installer/v1/push/fetch-subscription": {"namespace": "otgs/installer/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/otgs/installer/v1/push/fetch-subscription"}]}}, "/facetwp/v1": {"namespace": "facetwp/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "facetwp/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/facetwp/v1"}]}}, "/facetwp/v1/refresh": {"namespace": "facetwp/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/facetwp/v1/refresh"}]}}, "/facetwp/v1/fetch": {"namespace": "facetwp/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/facetwp/v1/fetch"}]}}, "/wp/v2": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp/v2", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2"}]}}, "/wp/v2/posts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "dp-rewrite-republish", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "基于多个分类法间的关系限制结果集。", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "将结果集限制为在 categories 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "是否在限制结果集的项目中包含子项目。", "type": "boolean", "default": false}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "将结果集限制为在 categories 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "是否在限制结果集的项目中包含子项目。", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}, "tags": {"description": "将结果集限制为在 tags 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "tags_exclude": {"description": "将结果集限制为在 tags 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "sticky": {"description": "将结果集限制为置顶项目。", "type": "boolean", "required": false}, "ignore_sticky": {"description": "Whether to ignore sticky posts or not.", "type": "boolean", "default": false, "required": false}, "format": {"description": "将结果集限制为分配一个或多个指定格式的项。", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "文章的形式。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "文章是否为置顶。", "type": "boolean", "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "categories": {"description": "在 category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "在 post_tag 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/posts"}]}}, "/wp/v2/posts/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "覆盖默认摘要长度。", "type": "integer", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "文章的形式。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "文章是否为置顶。", "type": "boolean", "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "categories": {"description": "在 category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "在 post_tag 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/posts/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "文章的形式。", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "sticky": {"description": "文章是否为置顶。", "type": "boolean", "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "categories": {"description": "在 category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "在 post_tag 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/pages": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "将结果集限制为有特定 menu_order 的文章。", "type": "integer", "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "parent": {"description": "将结果集限制为具有特定父 ID 的项目。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "将结果集限制为除特定父 ID 之外的所有项。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "dp-rewrite-republish", "any"], "type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "parent": {"description": "文章的父级 ID。", "type": "integer", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "文章与其他文章的顺序。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/pages"}]}}, "/wp/v2/pages/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "覆盖默认摘要长度。", "type": "integer", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "parent": {"description": "文章的父级 ID。", "type": "integer", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "文章与其他文章的顺序。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/pages/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "文章的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "文章与其他文章的顺序。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/media": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定作者的文章。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "将结果集限制为具有特定父 ID 的项目。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "将结果集限制为除特定父 ID 之外的所有项。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "inherit", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["inherit", "private", "trash"], "type": "string"}, "required": false}, "media_type": {"default": null, "description": "将结果集限制为某一媒体类型的附件。", "type": "string", "enum": ["image", "video", "audio", "application", "text"], "required": false}, "mime_type": {"default": null, "description": "将结果集限制为某一 MIME 类型的附件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "alt_text": {"description": "在附件未能显示时显示的替换文本。", "type": "string", "required": false}, "caption": {"description": "附件说明文字。", "type": "object", "properties": {"raw": {"description": "附件的说明文字，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 说明文字，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "附件的描述。", "type": "object", "properties": {"raw": {"description": "附件的描述，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 描述，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "附件所属文章的 ID。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/media"}]}}, "/wp/v2/media/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "文章作者的 ID。", "type": "integer", "required": false}, "featured_media": {"description": "文章的特色媒体 ID。", "type": "integer", "required": false}, "comment_status": {"description": "文章是否开放评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "文章是否接受 Ping。", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "alt_text": {"description": "在附件未能显示时显示的替换文本。", "type": "string", "required": false}, "caption": {"description": "附件说明文字。", "type": "object", "properties": {"raw": {"description": "附件的说明文字，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 说明文字，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "附件的描述。", "type": "object", "properties": {"raw": {"description": "附件的描述，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "附件的 HTML 描述，经转换后用于显示。", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "附件所属文章的 ID。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/post-process": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "附件的唯一标识符。", "type": "integer", "required": false}, "action": {"type": "string", "enum": ["create-image-subsizes"], "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/edit": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"src": {"description": "已经编辑的图片文件 的 URL。", "type": "string", "format": "uri", "required": true}, "modifiers": {"description": "图片编辑数组。", "type": "array", "minItems": 1, "items": {"description": "图片编辑", "type": "object", "required": ["type", "args"], "oneOf": [{"title": "旋转", "properties": {"type": {"description": "旋转类型。", "type": "string", "enum": ["rotate"]}, "args": {"description": "旋转参数。", "type": "object", "required": ["angle"], "properties": {"angle": {"description": "顺时针旋转的角度。", "type": "number"}}}}}, {"title": "裁剪", "properties": {"type": {"description": "裁剪方式。", "type": "string", "enum": ["crop"]}, "args": {"description": "裁剪参数。", "type": "object", "required": ["left", "top", "width", "height"], "properties": {"left": {"description": "从左边开始裁剪的水平位置以占用图片宽度的百分比计算。", "type": "number"}, "top": {"description": "从顶部开始裁剪的垂直位置以占用图片高度的百分比计算。", "type": "number"}, "width": {"description": "裁剪宽度以占用图片宽度的百分比计算。", "type": "number"}, "height": {"description": "裁剪的高度以占用图片高度的百分比计算。", "type": "number"}}}}}]}, "required": false}, "rotation": {"description": "顺时针旋转的角度量。已弃用：请改为使用 `modifiers` 参数。", "type": "integer", "minimum": 0, "exclusiveMinimum": true, "maximum": 360, "exclusiveMaximum": true, "required": false}, "x": {"description": "从 X 轴开始裁剪的位置以占用图片的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "y": {"description": "从 Y 轴开始裁剪的位置以占用图片的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "width": {"description": "裁剪宽度以图片所占用的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "height": {"description": "裁剪高度以图片所站用的百分比计算。已弃用。使用`modifiers`代替。", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/menu-items": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "menu_order", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "dp-rewrite-republish", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "基于多个分类法间的关系限制结果集。", "type": "string", "enum": ["AND", "OR"], "required": false}, "menus": {"description": "将结果集限制为在 menus 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "menus_exclude": {"description": "将结果集限制为在 menus 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "menu_order": {"description": "将结果集限制为有特定 menu_order 的文章。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"title": {"description": "对象的标题。", "type": ["string", "object"], "properties": {"raw": {"description": "对象的标题，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "对象的 HTML 标题，转换后显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"default": "custom", "description": "原始表示的对象组，如「post_type」或「taxonomy」。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"default": "publish", "description": "对象的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "parent": {"default": 0, "description": "对象的父对象 ID。", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "此菜单项的 link 元素的 title 属性的文本。", "type": "string", "required": false}, "classes": {"description": "此菜单项的链接元素的类名称。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "此菜单项的描述。", "type": "string", "required": false}, "menu_order": {"default": 1, "description": "导航菜单项的 nav_menu_item 的 DB ID（如果有），否则为 0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "对象初始的表示类型，如 「分类」、「文章」和「附件」。", "type": "string", "required": false}, "object_id": {"default": 0, "description": "此菜单项表示的原始对象的数据库 ID，例如文章的 ID 或分类的 term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "此菜单项的链接元素的 target 属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "该菜单项指向的 URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "该菜单项的链接中表示的 XFN 关系。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "此对象在 nav_menu 分类法中指定的项。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/menu-items"}]}}, "/wp/v2/menu-items/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "title": {"description": "对象的标题。", "type": ["string", "object"], "properties": {"raw": {"description": "对象的标题，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "对象的 HTML 标题，转换后显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "原始表示的对象组，如「post_type」或「taxonomy」。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "对象的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "parent": {"description": "对象的父对象 ID。", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "此菜单项的 link 元素的 title 属性的文本。", "type": "string", "required": false}, "classes": {"description": "此菜单项的链接元素的类名称。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "此菜单项的描述。", "type": "string", "required": false}, "menu_order": {"description": "导航菜单项的 nav_menu_item 的 DB ID（如果有），否则为 0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "对象初始的表示类型，如 「分类」、「文章」和「附件」。", "type": "string", "required": false}, "object_id": {"description": "此菜单项表示的原始对象的数据库 ID，例如文章的 ID 或分类的 term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "此菜单项的链接元素的 target 属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "该菜单项指向的 URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "该菜单项的链接中表示的 XFN 关系。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "此对象在 nav_menu 分类法中指定的项。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/menu-items/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "对象的父对象 ID。", "type": "integer", "minimum": 0, "required": false}, "title": {"description": "对象的标题。", "type": ["string", "object"], "properties": {"raw": {"description": "对象的标题，存放于数据库。", "type": "string", "context": ["edit"]}, "rendered": {"description": "对象的 HTML 标题，转换后显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "原始表示的对象组，如「post_type」或「taxonomy」。", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "对象的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "attr_title": {"description": "此菜单项的 link 元素的 title 属性的文本。", "type": "string", "required": false}, "classes": {"description": "此菜单项的链接元素的类名称。", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "此菜单项的描述。", "type": "string", "required": false}, "menu_order": {"description": "导航菜单项的 nav_menu_item 的 DB ID（如果有），否则为 0。", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "对象初始的表示类型，如 「分类」、「文章」和「附件」。", "type": "string", "required": false}, "object_id": {"description": "此菜单项表示的原始对象的数据库 ID，例如文章的 ID 或分类的 term_id。", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "此菜单项的链接元素的 target 属性。", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "该菜单项指向的 URL。", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "该菜单项的链接中表示的 XFN 关系。", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "此对象在 nav_menu 分类法中指定的项。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/menu-items/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "dp-rewrite-republish", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "基于多个分类法间的关系限制结果集。", "type": "string", "enum": ["AND", "OR"], "required": false}, "wp_pattern_category": {"description": "将结果集限制为在 wp_pattern_category 分类法中指定了特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "项目是否必须分配所有或任何指定的术语。", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "wp_pattern_category_exclude": {"description": "将结果集限制为在 wp_pattern_category 分类法中未指定特定项目的项目。", "type": ["object", "array"], "oneOf": [{"title": "术语 ID 列表", "description": "将项目与列出的 ID 相匹配。", "type": "array", "items": {"type": "integer"}}, {"title": "术语 ID 分类法查询。", "description": "进行一项高级项目查询。", "type": "object", "properties": {"terms": {"description": "术语 ID。", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wp_pattern_category": {"description": "在 wp_pattern_category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/blocks"}]}}, "/wp/v2/blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "覆盖默认摘要长度。", "type": "integer", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wp_pattern_category": {"description": "在 wp_pattern_category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "该篇文章的摘要。", "type": "object", "properties": {"raw": {"description": "文章的摘要，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "文章的 HTML 摘要，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "摘要是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"_acf_changed": {"type": "boolean", "title": "", "description": "", "default": false}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wp_pattern_category": {"description": "在 wp_pattern_category 分类法中分配给该文章的项目。", "type": "array", "items": {"type": "integer"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/templates": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "限制为指定的文章 ID。", "type": "integer", "required": false}, "area": {"description": "限制为指定的模板组件区。", "type": "string", "required": false}, "post_type": {"description": "要获取模板的文章类型。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"default": "", "description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "模板的描述。", "type": "string", "required": false}, "status": {"default": "publish", "description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/templates"}]}}, "/wp/v2/templates/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "获取回退所用的模板的别名：", "type": "string", "required": true}, "is_custom": {"description": "指明模板是自定义的，或是属于模板层级的一部分", "type": "boolean", "required": false}, "template_prefix": {"description": "所创建模板的模板前缀。 这被用于提取主模板类型，例如在「taxonomy-books」中将提取「taxonomy」。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/templates/lookup"}]}}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "area": {"description": "模板组件用途（页眉、页脚等）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "该模板的 ID", "type": "string", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/template-parts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "限制为指定的文章 ID。", "type": "integer", "required": false}, "area": {"description": "限制为指定的模板组件区。", "type": "string", "required": false}, "post_type": {"description": "要获取模板的文章类型。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"default": "", "description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "模板的描述。", "type": "string", "required": false}, "status": {"default": "publish", "description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "area": {"description": "模板组件用途（页眉、页脚等）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/template-parts"}]}}, "/wp/v2/template-parts/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "获取回退所用的模板的别名：", "type": "string", "required": true}, "is_custom": {"description": "指明模板是自定义的，或是属于模板层级的一部分", "type": "boolean", "required": false}, "template_prefix": {"description": "所创建模板的模板前缀。 这被用于提取主模板类型，例如在「taxonomy-books」中将提取「taxonomy」。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/template-parts/lookup"}]}}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "slug": {"description": "标识模板的唯一别名。", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "模板的主题标识符。", "type": "string", "required": false}, "type": {"description": "模板的类型。", "type": "string", "required": false}, "content": {"description": "模板的内容。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的内容，因为它存在于数据库中。", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "模板使用的内容块格式的版本。", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "模板的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "模板的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "模板的 HTML 标题，已转换以供显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "模板的描述。", "type": "string", "required": false}, "status": {"description": "模板的状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "author": {"description": "模板作者的 ID。", "type": "integer", "required": false}, "area": {"description": "模板组件用途（页眉、页脚等）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "全局样式修订版父版本的 ID。", "type": "integer", "required": false}, "id": {"description": "全局样式修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[\\/\\s%\\w\\.\\(\\)\\[\\]\\@_\\-]+)/variations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "主题标识符", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "主题标识符", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/global-styles/(?P<id>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"id": {"description": "该模板的 ID", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": false}, "args": {"styles": {"description": "全局样式。", "type": ["object"], "required": false}, "settings": {"description": "全局设置。", "type": ["object"], "required": false}, "title": {"description": "全局样式变体的标题。", "type": ["object", "string"], "properties": {"raw": {"description": "全局样式变体的标题，因为它存在于数据库中。", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/navigation": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之后修改过的文章。", "type": "string", "format": "date-time", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的文章。", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "将回应限制为一个给定的日期（ISO 8601 兼容格式）之前修改过的文章。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "需要搜索的栏位名称组。", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "将结果集限制为有一个或多个状态的文章。", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "acf-disabled", "dp-rewrite-republish", "any"], "type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/navigation"}]}}, "/wp/v2/navigation/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按对象属性排序集合。", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "修订版的父级 ID。", "type": "integer", "required": false}, "id": {"description": "修订版的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为修订版本不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/navigation/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "date": {"description": "文章发布的日期（站点时区）。", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "该文章发布的 GMT 日期。", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "文章的字母数字标识符，其类型是唯一的。", "type": "string", "required": false}, "status": {"description": "文章的命名状态。", "type": "string", "enum": ["publish", "future", "draft", "pending", "private", "acf-disabled"], "required": false}, "password": {"description": "用来保护内容和摘要的密码。", "type": "string", "required": false}, "title": {"description": "文章的标题。", "type": "object", "properties": {"raw": {"description": "文章的标题，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 标题，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "文章的内容。", "type": "object", "properties": {"raw": {"description": "文章的内容，存放于数据库中。", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "文章的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "该文章所使用的内容区块格式版本。", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "内容是否受到密码保护。", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "用于显示文章的主题文件。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "自动保存的父级 ID。", "type": "integer", "required": false}, "id": {"description": "自动保存的 ID。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/font-families": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "slug": {"description": "将结果集限制为有一个或多个特定别名的文章。", "type": "array", "items": {"type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"theme_json_version": {"description": "用于排版设置的 theme.json 模式版本。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "以字符串形式编码的 theme.json 格式的 font-family 声明。", "type": "string", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/font-families"}]}}, "/wp/v2/font-families/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "theme_json_version": {"description": "用于排版设置的 theme.json 模式版本。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "以字符串形式编码的 theme.json 格式的 font-family 声明。", "type": "string", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "文章的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "如何解释搜索输入。", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "theme_json_version": {"description": "用于排版设置的 theme.json 模式版本。", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_face_settings": {"description": "在 theme.json 格式中以字符串形式编码的 font-face 声明。", "type": "string", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "id": {"description": "字体的唯一标识符。", "type": "integer", "required": true}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"font_family_id": {"description": "字体面的父字体系列的 ID。", "type": "integer", "required": true}, "id": {"description": "字体的唯一标识符。", "type": "integer", "required": true}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/types"}]}}, "/wp/v2/types/(?P<type>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"description": "文章类型的英数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/statuses": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/statuses"}]}}, "/wp/v2/statuses/(?P<status>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "状态的英数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/taxonomies": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "type": {"description": "限制结果为关联到特定文章类型的分类法。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/taxonomies"}]}}, "/wp/v2/taxonomies/(?P<taxonomy>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"description": "分类法的英数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/categories": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "parent": {"description": "将结果集限制为指定给特定父项目的项目。", "type": "integer", "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "parent": {"description": "父术语 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/categories"}]}}, "/wp/v2/categories/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "parent": {"description": "父术语 ID。", "type": "integer", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/tags": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/tags"}]}}, "/wp/v2/tags/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/menus": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "locations": {"description": "分配给菜单的位置。", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "是否自动添加顶级页面到此菜单。", "type": "boolean", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/menus"}]}}, "/wp/v2/menus/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "locations": {"description": "分配给菜单的位置。", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "是否自动添加顶级页面到此菜单。", "type": "boolean", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/wp_pattern_category": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按项目属性排序集合。", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "是否隐藏未被指定到任何文章的项目。", "type": "boolean", "default": false, "required": false}, "post": {"description": "将结果集限制为指定给特定文章的项目。", "type": "integer", "default": null, "required": false}, "slug": {"description": "将结果集限制为具有一个或多个别名的分类法项目。", "type": "array", "items": {"type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": true}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/wp_pattern_category"}]}}, "/wp/v2/wp_pattern_category/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "description": {"description": "项目的 HTML 描述。", "type": "string", "required": false}, "name": {"description": "项目的 HTML 标题。", "type": "string", "required": false}, "slug": {"description": "项目类型而言的英数字标识符。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "项目的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "要求为 true，因为项目不能被移动到回收站。", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/users/me": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"username": {"description": "用户的登录名。", "type": "string", "required": false}, "name": {"description": "用户的显示名。", "type": "string", "required": false}, "first_name": {"description": "用户的名字。", "type": "string", "required": false}, "last_name": {"description": "用户的姓氏。", "type": "string", "required": false}, "email": {"description": "用户的邮箱地址。", "type": "string", "format": "email", "required": false}, "url": {"description": "用户的 URL。", "type": "string", "format": "uri", "required": false}, "description": {"description": "用户的描述。", "type": "string", "required": false}, "locale": {"description": "用户的地区语言。", "type": "string", "enum": ["", "en_US", "ar", "en_GB", "hi_IN", "id_ID", "it_IT", "ja", "th", "vi", "zh_CN", "zh_TW"], "required": false}, "nickname": {"description": "用户的昵称。", "type": "string", "required": false}, "slug": {"description": "用户的英数字标识符。", "type": "string", "required": false}, "roles": {"description": "用户被赋予的角色。", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "用户的密码（从不被包含）。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "日期和时间的偏好设置已更新。", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"force": {"type": "boolean", "default": false, "description": "要求为 true，因为用户不能被移动到回收站。", "required": false}, "reassign": {"type": "integer", "description": "将被删除用户的文章和链接重新指定到此用户 ID。", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/users/me"}]}}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords": {"namespace": "wp/v2", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"app_id": {"description": "由应用程序提供的用于唯一识别的 UUID。建议使用含有 URL 或 DNS 命名空间的 UUID v5。", "type": "string", "format": "uuid", "required": false}, "name": {"description": "应用程序密码名称。", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/introspect": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/(?P<uuid>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"app_id": {"description": "由应用程序提供的用于唯一识别的 UUID。建议使用含有 URL 或 DNS 命名空间的 UUID v5。", "type": "string", "format": "uuid", "required": false}, "name": {"description": "应用程序密码名称。", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/comments": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "after": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之后发布的评论。", "type": "string", "format": "date-time", "required": false}, "author": {"description": "将结果集限制为指定给特定用户 ID 的评论，需要授权。", "type": "array", "items": {"type": "integer"}, "required": false}, "author_exclude": {"description": "确保结果集排除指定给特定用户 ID 的评论，需要授权。", "type": "array", "items": {"type": "integer"}, "required": false}, "author_email": {"default": null, "description": "将结果集限制为指定作者的邮箱地址，需要授权。", "format": "email", "type": "string", "required": false}, "before": {"description": "将回应限制在一个给定的 ISO8601 兼容日期之前发布的评论。", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按评论属性对集合进行排序。", "type": "string", "default": "date_gmt", "enum": ["date", "date_gmt", "id", "include", "post", "parent", "type"], "required": false}, "parent": {"default": [], "description": "将结果集限制为指定父 ID 的评论。", "type": "array", "items": {"type": "integer"}, "required": false}, "parent_exclude": {"default": [], "description": "确保结果集排除指定父 ID 的评论。", "type": "array", "items": {"type": "integer"}, "required": false}, "post": {"default": [], "description": "将结果集限制为关联到指定文章 ID 的评论。", "type": "array", "items": {"type": "integer"}, "required": false}, "status": {"default": "approve", "description": "将结果集限制为设置特定状态的评论，需要授权。", "type": "string", "required": false}, "type": {"default": "comment", "description": "将结果集限制为设置特定类型的评论，需要授权。", "type": "string", "required": false}, "password": {"description": "该文章的密码，如文章受密码保护。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"author": {"description": "用户对象的 ID，如果作者是用户。", "type": "integer", "required": false}, "author_email": {"description": "评论者的邮箱地址。", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "评论者的 IP 地址。", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "评论者的显示名。", "type": "string", "required": false}, "author_url": {"description": "评论者的网址。", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "评论者的 User-Agent。", "type": "string", "required": false}, "content": {"description": "评论的内容。", "type": "object", "properties": {"raw": {"description": "评论的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "评论的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "评论发表的日期（站点时区）。", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "评论发表的 GMT 日期。", "type": "string", "format": "date-time", "required": false}, "parent": {"default": 0, "description": "评论的父级 ID。", "type": "integer", "required": false}, "post": {"default": 0, "description": "关联文章对象的 ID。", "type": "integer", "required": false}, "status": {"description": "评论的状态。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/comments"}]}}, "/wp/v2/comments/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "评论的唯一标识符。", "type": "integer", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "评论所属文章的密码（如果该文章被密码保护）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "评论的唯一标识符。", "type": "integer", "required": false}, "author": {"description": "用户对象的 ID，如果作者是用户。", "type": "integer", "required": false}, "author_email": {"description": "评论者的邮箱地址。", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "评论者的 IP 地址。", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "评论者的显示名。", "type": "string", "required": false}, "author_url": {"description": "评论者的网址。", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "评论者的 User-Agent。", "type": "string", "required": false}, "content": {"description": "评论的内容。", "type": "object", "properties": {"raw": {"description": "评论的内容，存放于数据库中。", "type": "string", "context": ["edit"]}, "rendered": {"description": "评论的 HTML 内容，经转换后用于显示。", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "评论发表的日期（站点时区）。", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "评论发表的 GMT 日期。", "type": "string", "format": "date-time", "required": false}, "parent": {"description": "评论的父级 ID。", "type": "integer", "required": false}, "post": {"description": "关联文章对象的 ID。", "type": "integer", "required": false}, "status": {"description": "评论的状态。", "type": "string", "required": false}, "meta": {"description": "元字段。", "type": "object", "properties": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "评论的唯一标识符。", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "是否绕过回收站并强行删除。", "required": false}, "password": {"description": "评论所属文章的密码（如果该文章被密码保护）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "type": {"default": "post", "description": "限制结果为一种对象类型。", "type": "string", "enum": ["post", "term", "post-format"], "required": false}, "subtype": {"default": "any", "description": "限制结果为一种或多种对象子类型。", "type": "array", "items": {"enum": ["post", "page", "category", "post_tag", "any"], "type": "string"}, "required": false}, "exclude": {"description": "确保结果集排除指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "将结果集限制为指定 ID。", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/search"}]}}, "/wp/v2/block-renderer/(?P<name>[a-z0-9-]+/[a-z0-9-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET", "POST"], "args": {"name": {"description": "此区块的唯一注册名。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["edit"], "default": "view", "required": false}, "attributes": {"description": "此区块的属性。", "type": "object", "default": [], "required": false}, "post_id": {"description": "文章上下文的 ID。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/block-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "区块命名空间。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/block-types"}]}}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "区块命名空间。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)/(?P<name>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"name": {"description": "区块名称", "type": "string", "required": false}, "namespace": {"description": "区块命名空间。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/settings": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"title": {"title": "标题", "description": "站点标题。", "type": "string", "required": false}, "description": {"title": "副标题", "description": "站点副标题。", "type": "string", "required": false}, "timezone": {"title": "", "description": "和您在同一个时区的城市。", "type": "string", "required": false}, "date_format": {"title": "", "description": "对所有日期字符串适用的日期格式。", "type": "string", "required": false}, "time_format": {"title": "", "description": "对所有时间字符串适用的时间格式。", "type": "string", "required": false}, "start_of_week": {"title": "", "description": "一周从周几开始。", "type": "integer", "required": false}, "language": {"title": "", "description": "WordPress 地区语言代码。", "type": "string", "required": false}, "use_smilies": {"title": "", "description": "将表情符号如:-) 和:-P 转换为图片显示。", "type": "boolean", "required": false}, "default_category": {"title": "", "description": "默认文章分类。", "type": "integer", "required": false}, "default_post_format": {"title": "", "description": "默认文章形式。", "type": "string", "required": false}, "posts_per_page": {"title": "每页最多文章数", "description": "最多显示的博客页面数量。", "type": "integer", "required": false}, "show_on_front": {"title": "在前面显示", "description": "需要在首页上显示的项目", "type": "string", "required": false}, "page_on_front": {"title": "前一页", "description": "需要在首页上显示的页面 ID", "type": "integer", "required": false}, "page_for_posts": {"title": "", "description": "需要显示最新文章的页面 ID", "type": "integer", "required": false}, "default_ping_status": {"title": "", "description": "允许其他博客发送链接通知（pingback 和 trackback）到新文章。", "type": "string", "enum": ["open", "closed"], "required": false}, "default_comment_status": {"title": "允许对新文章发表评论", "description": "允许他人在新文章上发表评论。", "type": "string", "enum": ["open", "closed"], "required": false}, "site_logo": {"title": "Logo", "description": "站点 logo", "type": "integer", "required": false}, "site_icon": {"title": "图标", "description": "站点图标。", "type": "integer", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/settings"}]}}, "/wp/v2/themes": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "将结果集限制为指定了一个或多个状态的主题。", "type": "array", "items": {"enum": ["active", "inactive"], "type": "string"}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/themes"}]}}, "/wp/v2/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"stylesheet": {"description": "主题的样式表。这是主题的唯一标识。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/plugins": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "required": false}, "status": {"description": "限制结果集为具有给定状态的插件。", "type": "array", "items": {"type": "string", "enum": ["inactive", "active", "network-active"]}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "args": {"slug": {"type": "string", "description": "WordPress.org 插件目录别名。", "pattern": "[\\w\\-]+", "required": true}, "status": {"description": "插件启用状态。", "type": "string", "enum": ["inactive", "active", "network-active"], "default": "inactive", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/plugins"}]}}, "/wp/v2/plugins/(?P<plugin>[^.\\/]+(?:\\/[^.\\/]+)?)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "status": {"description": "插件启用状态。", "type": "string", "enum": ["inactive", "active", "network-active"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/sidebars": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/sidebars"}]}}, "/wp/v2/sidebars/(?P<id>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "已注册的边栏 ID", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"widgets": {"description": "嵌套小工具。", "type": "array", "items": {"type": ["object", "string"]}, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/widget-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/widget-types"}]}}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "小工具类型 ID。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/encode": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "小工具类型 ID。", "type": "string", "required": true}, "instance": {"description": "小工具的当前设置实例。", "type": "object", "required": false}, "form_data": {"description": "序列化小工具表单数据，以编码为设置实例。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/render": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "小工具类型 ID。", "type": "string", "required": true}, "instance": {"description": "小工具的当前设置实例。", "type": "object", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/widgets": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "sidebar": {"description": "返回小工具的侧边栏。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"id": {"description": "小工具的唯一标识符。", "type": "string", "required": false}, "id_base": {"description": "小工具的类型。对应于 widget-types 端点的 ID。", "type": "string", "required": false}, "sidebar": {"default": "wp_inactive_widgets", "description": "小工具所属的侧边栏。", "type": "string", "required": true}, "instance": {"description": "小工具的实例设置（如果支持）。", "type": "object", "properties": {"encoded": {"description": "实例设置的 Base64 编码表示。", "type": "string", "context": ["edit"]}, "hash": {"description": "实例设置的加密哈希值。", "type": "string", "context": ["edit"]}, "raw": {"description": "未编码的实例设置（如果支持）。", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "来自小工具管理表单的 URL 编码表单数据。用于更新不支持实例的小工具（指写）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/widgets"}]}}, "/wp/v2/widgets/(?P<id>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "小工具的唯一标识符。", "type": "string", "required": false}, "id_base": {"description": "小工具的类型。对应于 widget-types 端点的 ID。", "type": "string", "required": false}, "sidebar": {"description": "小工具所属的侧边栏。", "type": "string", "required": false}, "instance": {"description": "小工具的实例设置（如果支持）。", "type": "object", "properties": {"encoded": {"description": "实例设置的 Base64 编码表示。", "type": "string", "context": ["edit"]}, "hash": {"description": "实例设置的加密哈希值。", "type": "string", "context": ["edit"]}, "raw": {"description": "未编码的实例设置（如果支持）。", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "来自小工具管理表单的 URL 编码表单数据。用于更新不支持实例的小工具（指写）。", "type": "string", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"force": {"description": "强行移除小工具，货将其移动到未启用的小工具侧边栏。", "type": "boolean", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp/v2/block-directory/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "term": {"description": "将结果集限制为匹配搜索词的区块。", "type": "string", "minLength": 1, "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/block-directory/search"}]}}, "/wp/v2/pattern-directory/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "将结果限制为匹配字符串的。", "type": "string", "minLength": 1, "required": false}, "category": {"description": "将结果限制为与分类 ID 匹配的结果。", "type": "integer", "minimum": 1, "required": false}, "keyword": {"description": "将结果限制为与关键字 ID 匹配的结果。", "type": "integer", "minimum": 1, "required": false}, "slug": {"description": "将结果限制为与样板（别名）匹配的结果。", "type": "array", "required": false}, "offset": {"description": "将结果集移位特定数量。", "type": "integer", "required": false}, "order": {"description": "设置排序字段升序或降序。", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "按文章属性对集合进行排序。", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "favorite_count"], "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/pattern-directory/patterns"}]}}, "/wp/v2/block-patterns/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/block-patterns/patterns"}]}}, "/wp/v2/block-patterns/categories": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/block-patterns/categories"}]}}, "/wp-site-health/v1": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-site-health/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1"}]}}, "/wp-site-health/v1/tests/background-updates": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/tests/background-updates"}]}}, "/wp-site-health/v1/tests/loopback-requests": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/tests/loopback-requests"}]}}, "/wp-site-health/v1/tests/https-status": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/tests/https-status"}]}}, "/wp-site-health/v1/tests/dotorg-communication": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/tests/dotorg-communication"}]}}, "/wp-site-health/v1/tests/authorization-header": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/tests/authorization-header"}]}}, "/wp-site-health/v1/directory-sizes": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/directory-sizes"}]}}, "/wp-site-health/v1/tests/page-cache": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-site-health/v1/tests/page-cache"}]}}, "/wp-block-editor/v1": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-block-editor/v1", "required": false}, "context": {"default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-block-editor/v1"}]}}, "/wp-block-editor/v1/url-details": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "要处理的 URL。", "type": "string", "format": "uri", "required": true}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-block-editor/v1/url-details"}]}}, "/wp/v2/menu-locations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/menu-locations"}]}}, "/wp/v2/menu-locations/(?P<location>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"location": {"description": "菜单位置的字母数字标识符。", "type": "string", "required": false}, "context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}, "/wp-block-editor/v1/export": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-block-editor/v1/export"}]}}, "/wp-block-editor/v1/navigation-fallback": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp-block-editor/v1/navigation-fallback"}]}}, "/wp/v2/font-collections": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "集合的当前页。", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "结果集包含的最大项目数量。", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}], "_links": {"self": [{"href": "https://www.crownrms.com/cn/wp-json/wp/v2/font-collections"}]}}, "/wp/v2/font-collections/(?P<slug>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "请求提出的范围，用于决定回应包含的字段。", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wpml_language": {"type": "string", "description": "WPML's language code", "enum": ["zh-hans"], "required": false}}}]}}, "site_logo": 0, "site_icon": 0, "site_icon_url": "", "_links": {"help": [{"href": "https://developer.wordpress.org/rest-api/"}]}}